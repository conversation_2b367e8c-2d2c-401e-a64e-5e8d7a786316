from langchain.tools import Tool
import json
from datetime import datetime
import logging

class HealthScoreAnalysisTool:
    """
    A comprehensive tool that combines vitals, lifestyle, and test results into a dynamic health score.
    Provides holistic health awareness through a weighted scoring engine.
    """

    def __init__(self):
        # Define base scoring criteria for vital signs and tests (using standard reference units)
        self.scoring_criteria = {
            "Glucose": {
                "range": (70, 100),
                "unit": "mg/dL, mmol/L",
                "type": "float",
                "supported_units": ["mg/dL", "mg/dl", "mmol/L", "mmol/l"]
            },
            "Hepatitis B": {"range": "Negative", "unit": "Binary", "type": "string"},
            "Systolic": {"range": (90, 140), "unit": "mmHg", "type": "int"},
            "Diastolic": {"range": (60, 90), "unit": "mmHg", "type": "int"},
            "Waist Circumference": {"range": (70, 102), "unit": "cm", "type": "float"},
            "Hiv": {"range": "Negative", "unit": "Binary", "type": "string"},
            "Fev": {
                "range": (2.5, 5.0),
                "unit": "L, mL",
                "type": "float",
                "supported_units": ["L", "l", "mL", "ml", "milliliter", "millilitre", "liter", "litre"]
            },
            "Temperature": {
                "range": (36.1, 37.2),
                "unit": "°C, °F",
                "type": "float",
                "supported_units": ["°C", "°F", "C", "F", "celsius", "fahrenheit"]
            },
            "Ecg": {"range": (60, 100), "unit": "BPM", "type": "float"},
            "Spo2": {"range": (95, 100), "unit": "%", "type": "float"},
            "Weight": {
                "range": (45, 90),
                "unit": "kg, lb",
                "type": "float",
                "supported_units": ["kg", "lb", "lbs", "pound", "pounds", "kilogram", "kilograms"]
            },
            "Widal Test": {"range": "Negative", "unit": "Antibody Panel", "type": "dict"},
            "Malaria": {"range": "Negative", "unit": "Binary", "type": "string"},
            "Kidney": {"range": (0.6, 1.2), "unit": "mg/dL", "type": "float"},
            "Lipid": {"range": (0, 200), "unit": "mg/dL", "type": "float"},
            "Liver": {"range": (7, 56), "unit": "U/L", "type": "float"},
            "Respiratory": {"range": (12, 20), "unit": "breaths/min", "type": "float"},
        }

        # Define unit conversion factors and supported units (aligned with scoring_criteria)
        self.unit_conversions = {
            "Glucose": {
                "description": "Blood glucose levels",
                "standard_unit": "mg/dL",
                "supported_units": ["mg/dL", "mg/dl", "mmol/L", "mmol/l"],
                "conversion_factor": {"mmol/L": 18.0, "mmol/l": 18.0}  # multiply mmol/L by 18 to get mg/dL
            },
            "Temperature": {
                "description": "Body temperature",
                "standard_unit": "°C",
                "supported_units": ["°C", "°F", "C", "F", "celsius", "fahrenheit"],
                "conversion_function": {"°F": lambda f: (f - 32) * 5/9, "F": lambda f: (f - 32) * 5/9, "fahrenheit": lambda f: (f - 32) * 5/9}
            },
            "Fev": {
                "description": "Forced Expiratory Volume (FEV1)",
                "standard_unit": "L",
                "supported_units": ["L", "l", "mL", "ml", "milliliter", "millilitre", "liter", "litre"],
                "conversion_factor": {"mL": 0.001, "ml": 0.001, "milliliter": 0.001, "millilitre": 0.001}  # divide mL by 1000 to get L
            },
            "Weight": {
                "description": "Body weight",
                "standard_unit": "kg",
                "supported_units": ["kg", "lb", "lbs", "pound", "pounds", "kilogram", "kilograms"],
                "conversion_factor": {"lb": 0.453592, "lbs": 0.453592, "pound": 0.453592, "pounds": 0.453592}  # multiply lb by 0.453592 to get kg
            }
        }

        # Define weights for different health categories - evidence-based weighting
        self.category_weights = {
            "vitals": 0.40,           # 40% weight for vital signs (critical physiological indicators)
            "lifestyle": 0.35,        # 35% weight for lifestyle factors (preventive health determinants)
            "test_results": 0.25      # 25% weight for test results (diagnostic indicators)
        }

        # Define vital signs parameters and their weights
        self.vital_weights = {
            "Systolic": 0.12,
            "Diastolic": 0.12,
            "Ecg": 0.12,
            "Spo2": 0.12,
            "Temperature": 0.08,
            "Weight": 0.12,
            "Waist Circumference": 0.08,
            "Fev": 0.10,
            "Respiratory": 0.08
        }

        # Define lifestyle parameters and their weights
        self.lifestyle_weights = {
            "Exercise": 0.20,
            "Diet": 0.20,
            "Sleep": 0.15,
            "Stress": 0.12,
            "Hydration": 0.08,
            "Smoking": 0.10,
            "Alcohol": 0.08,
            "Social Connection": 0.07
        }

        # Define test results parameters and their weights
        self.test_weights = {
            "Glucose": 0.12,
            "Malaria": 0.05,
            "Widal Test": 0.03,
            "Hepatitis B": 0.05,
            "Hiv": 0.05,
            "Kidney": 0.18,
            "Lipid": 0.18,
            "Liver": 0.12
        }

        # Define normal ranges for lifestyle factors
        self.lifestyle_ranges = {
            "Exercise": {"range": (3, 5), "unit": "days/week"},
            "Diet": {"range": (3, 5), "unit": "score"},
            "Sleep": {"range": (7, 9), "unit": "hours"},
            "Stress": {"range": (1, 2), "unit": "score"},
            "Hydration": {"range": (8, 12), "unit": "cups"},
            "Smoking": {"range": (0, 0), "unit": "cigarettes"},
            "Alcohol": {"range": (0, 2), "unit": "drinks/day"},
            "Social Connection": {"range": (3, 5), "unit": "score"}
        }

        # Add ranges for integrated test results (removed - using direct numeric values now)

        # Define trend thresholds for significant changes
        self.trend_thresholds = {
            "score_change": 5,  # 5% change in overall score is significant
            "vital_change": 10,  # 10% change in vital parameter is significant
            "lifestyle_change": 15,  # 15% change in lifestyle factor is significant
            "test_change": 10  # 10% change in test result is significant
        }

    def parse_value_with_unit(self, metric, value):
        """Parse a value that might include units and convert to standard unit"""
        import re

        if not isinstance(value, str):
            return value  # Return as-is if not a string

        # Check if this metric supports unit conversion
        if metric not in self.unit_conversions:
            # Try to extract numeric value if it's a string with units we don't handle
            numeric_match = re.search(r'([\d.]+)', str(value))
            if numeric_match:
                try:
                    return float(numeric_match.group(1))
                except ValueError:
                    return value
            return value

        # Extract numeric value and unit from string
        # Pattern to match number followed by optional space and unit
        pattern = r'([\d.]+)\s*([a-zA-Z°/]+)?'
        match = re.search(pattern, str(value))

        if not match:
            return value  # Return original if no match

        numeric_value = float(match.group(1))
        unit = match.group(2).lower() if match.group(2) else ""

        # Handle specific metric conversions using the new structure
        if metric in self.unit_conversions:
            conversion_info = self.unit_conversions[metric]

            # Check for conversion factors (multiplicative conversions)
            if "conversion_factor" in conversion_info:
                for unit_key, factor in conversion_info["conversion_factor"].items():
                    if unit == unit_key.lower():
                        return numeric_value * factor

            # Check for conversion functions (functional conversions like temperature)
            if "conversion_function" in conversion_info:
                for unit_key, func in conversion_info["conversion_function"].items():
                    if unit == unit_key.lower():
                        return func(numeric_value)

            # If unit is supported but no conversion needed (already in standard unit)
            if unit in [u.lower() for u in conversion_info["supported_units"]]:
                return numeric_value

        # If no specific conversion found, return the numeric value (assume standard unit)
        return numeric_value

    def get_supported_units_info(self):
        """Return information about supported units for each metric"""
        units_info = {}

        for metric, criteria in self.scoring_criteria.items():
            if "supported_units" in criteria:
                units_info[metric] = {
                    "metric": metric,
                    "supported_units": criteria["supported_units"],
                    "display_units": criteria["unit"],
                    "standard_range": criteria["range"],
                    "type": criteria["type"]
                }

        # Add conversion info if available
        for metric in units_info:
            if metric in self.unit_conversions:
                conversion_info = self.unit_conversions[metric]
                units_info[metric].update({
                    "description": conversion_info.get("description", ""),
                    "standard_unit": conversion_info.get("standard_unit", "")
                })

        return units_info

    def evaluate_health_metric(self, metric, value):
        """Evaluates a health metric against its normal range."""
        # Parse value with unit conversion if applicable
        parsed_value = self.parse_value_with_unit(metric, value)

        # Check in scoring criteria first
        if metric in self.scoring_criteria:
            criteria = self.scoring_criteria[metric]
            expected_range = criteria["range"]

            if isinstance(expected_range, tuple):  # Numerical value check
                if not isinstance(parsed_value, (int, float)):
                    return "Invalid data"
                low, high = expected_range
                if low <= parsed_value <= high:
                    return "Normal"
                elif parsed_value < low:
                    return "Low"
                else:
                    return "High"

            elif isinstance(expected_range, str):  # Binary test check or special handling
                # Special handling for Widal Test with detailed antibody results
                if metric == "Widal Test" and isinstance(value, dict):
                    # Handle detailed Widal Test format
                    reactive_count = 0
                    total_antibodies = 0

                    for _, result in value.items():
                        if result not in ["Unknown", None, "", "null"]:
                            total_antibodies += 1
                            if result.lower() == "reactive":
                                reactive_count += 1

                    if total_antibodies == 0:
                        return "Unknown"  # All unknown
                    elif reactive_count == 0:
                        return "Negative"  # All non-reactive (healthy)
                    else:
                        return "Positive"  # Some reactive antibodies (potential infection)

                # Standard binary test handling
                elif str(parsed_value) == "Unknown":
                    return "Unknown"  # Treat Unknown as null value
                elif str(parsed_value) == expected_range:
                    return "Negative"
                else:
                    return "Positive"

        # Check in lifestyle ranges
        elif metric in self.lifestyle_ranges:
            criteria = self.lifestyle_ranges[metric]
            expected_range = criteria["range"]

            if isinstance(expected_range, tuple):
                if not isinstance(parsed_value, (int, float)):
                    return "Invalid data"
                low, high = expected_range
                if low <= parsed_value <= high:
                    return "Normal"
                elif parsed_value < low:
                    return "Low"
                else:
                    return "High"

        return "Unknown"

    def calculate_vital_score(self, health_data):
        """Calculate score for vital signs using weighted scoring"""
        vital_score = 0
        max_vital_score = 0
        vital_issues = []

        for vital, weight in self.vital_weights.items():
            if vital in health_data and health_data[vital] not in [None, "", "null", "Unknown"]:
                max_vital_score += weight
                value = health_data[vital]

                # Evaluate the vital sign (parse_value_with_unit is called inside evaluate_health_metric)
                status = self.evaluate_health_metric(vital, value)

                if status == "Normal" or status == "Negative":
                    vital_score += weight
                elif status in ["Low", "High", "Abnormal", "Positive"]:
                    vital_issues.append(f"{vital} ({status})")

        # Normalize score if we have data
        normalized_score = (vital_score / max_vital_score) * 100 if max_vital_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": vital_issues
        }

    def calculate_lifestyle_score(self, health_data):
        """Calculate score for lifestyle factors using weighted scoring"""
        lifestyle_score = 0
        max_lifestyle_score = 0
        lifestyle_issues = []

        for factor, weight in self.lifestyle_weights.items():
            if factor in health_data and health_data[factor] not in [None, "", "null", "Unknown"]:
                max_lifestyle_score += weight
                value = health_data[factor]

                # Evaluate the lifestyle factor (parse_value_with_unit is called inside evaluate_health_metric)
                status = self.evaluate_health_metric(factor, value)

                if status == "Normal":
                    lifestyle_score += weight
                else:
                    lifestyle_issues.append(f"{factor} ({status})")

        # Normalize score if we have data
        normalized_score = (lifestyle_score / max_lifestyle_score) * 100 if max_lifestyle_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": lifestyle_issues
        }

    def calculate_test_score(self, health_data):
        """Calculate score for test results using weighted scoring"""
        test_score = 0
        max_test_score = 0
        test_issues = []

        for test, weight in self.test_weights.items():
            if test in health_data and health_data[test] not in [None, "", "null", "Unknown"]:
                max_test_score += weight
                value = health_data[test]

                # Handle all test results using standard evaluation (parse_value_with_unit is called inside evaluate_health_metric)
                status = self.evaluate_health_metric(test, value)

                if status == "Normal" or status == "Negative":
                    test_score += weight
                elif status == "Unknown":
                    # Skip Unknown values - don't count them as issues
                    pass
                elif status in ["Low", "High", "Abnormal", "Positive"]:
                    # Special formatting for Widal Test with detailed results
                    if test == "Widal Test" and isinstance(value, dict) and status == "Positive":
                        reactive_antibodies = []
                        for antibody, result in value.items():
                            if result not in ["Unknown", None, "", "null"] and result.lower() == "reactive":
                                reactive_antibodies.append(antibody)
                        if reactive_antibodies:
                            test_issues.append(f"{test} (Reactive: {', '.join(reactive_antibodies)})")
                        else:
                            test_issues.append(f"{test} ({status})")
                    else:
                        test_issues.append(f"{test} ({status})")

        # Normalize score if we have data
        normalized_score = (test_score / max_test_score) * 100 if max_test_score > 0 else 0

        return {
            "score": normalized_score,
            "issues": test_issues
        }

    def generate_improvement_tips(self, vital_issues, lifestyle_issues, test_issues):
        """Generate personalized improvement tips based on identified issues"""
        tips = []

        # Track categories to avoid repetitive tips
        tip_categories = {
            "doctor_visit": False,
            "breathing": False,
            "diet": False,
            "exercise": False,
            "blood_pressure": False,
            "sleep": False,
            "stress": False,
            "hydration": False
        }

        # Generate tips for vital issues
        for issue in vital_issues:
            if "Systolic" in issue or "Diastolic" in issue:
                if not tip_categories["blood_pressure"]:
                    tips.append("🩸 To help manage your blood pressure, consider the DASH diet (rich in fruits, vegetables, and low-fat dairy), limit sodium to 1,500-2,300mg daily, and aim for regular physical activity.")
                    tip_categories["blood_pressure"] = True
            elif "Spo2" in issue:
                if not tip_categories["breathing"]:
                    tips.append("🫁 Practice diaphragmatic breathing exercises (belly breathing) for 5-10 minutes several times daily to help improve your oxygen saturation levels.")
                    tip_categories["breathing"] = True
            elif "Weight" in issue:
                if "High" in issue and not tip_categories["diet"] and not tip_categories["exercise"]:
                    tips.append("💪 Consider consulting with a nutritionist about a personalized meal plan and finding enjoyable physical activities that fit your lifestyle.")
                    tip_categories["diet"] = True
                    tip_categories["exercise"] = True
                elif "Low" in issue and not tip_categories["diet"]:
                    tips.append("🥗 Consider adding more nutrient-dense foods like nuts, avocados, and protein-rich meals to help you reach a healthier weight.")
                    tip_categories["diet"] = True
            elif "Glucose" in issue:
                if not tip_categories["diet"]:
                    tips.append("🍎 To help manage your glucose levels, focus on complex carbohydrates, limit added sugars, and pair carbs with proteins to slow absorption.")
                    tip_categories["diet"] = True

        # Generate tips for lifestyle issues
        for issue in lifestyle_issues:
            if "Exercise" in issue:
                if not tip_categories["exercise"]:
                    tips.append("🏃‍♀️ Try to incorporate at least 150 minutes of moderate activity per week, broken into sessions as short as 10 minutes if needed.")
                    tip_categories["exercise"] = True
            elif "Diet" in issue:
                if not tip_categories["diet"]:
                    tips.append("🥦 Focus on a balanced diet with plenty of vegetables, fruits, whole grains, lean proteins, and healthy fats while limiting processed foods.")
                    tip_categories["diet"] = True
            elif "Sleep" in issue:
                if not tip_categories["sleep"]:
                    tips.append("😴 Improve your sleep by maintaining a consistent schedule, creating a relaxing bedtime routine, and keeping your bedroom cool, dark, and quiet.")
                    tip_categories["sleep"] = True
            elif "Stress" in issue:
                if not tip_categories["stress"]:
                    tips.append("🧘‍♀️ Practice stress management techniques like mindfulness meditation, deep breathing, or progressive muscle relaxation for at least 10 minutes daily.")
                    tip_categories["stress"] = True
            elif "Hydration" in issue:
                if not tip_categories["hydration"]:
                    tips.append("💧 Improve hydration by carrying a reusable water bottle, setting reminders to drink throughout the day, and eating water-rich foods like cucumbers and watermelon.")
                    tip_categories["hydration"] = True
            elif "Smoking" in issue:
                tips.append("🚭 Consider talking to your healthcare provider about smoking cessation programs, nicotine replacement therapy, or medications that can help you quit.")

        # Generate tips for test issues
        for issue in test_issues:
            if any(test in issue for test in ["Malaria", "Widal Test", "Hepatitis B", "Hiv"]):
                if not tip_categories["doctor_visit"]:
                    tips.append("🩺 Your test results require medical attention. Schedule an appointment with your healthcare provider to discuss these findings and appropriate next steps.")
                    tip_categories["doctor_visit"] = True
            elif "Kidney" in issue:
                tips.append("🧪 To support kidney health, stay well-hydrated, limit sodium and processed foods, and follow up with a nephrologist for personalized recommendations.")
            elif "Lipid" in issue:
                tips.append("❤️ To improve your cholesterol levels, focus on heart-healthy foods like fatty fish, nuts, olive oil, and fiber-rich fruits and vegetables.")
            elif "Liver" in issue:
                tips.append("🫀 To support liver health, limit alcohol consumption, maintain a healthy weight, and eat a balanced diet rich in antioxidants.")

        # If no tips were generated but there are issues
        if not tips and (vital_issues or lifestyle_issues or test_issues):
            tips.append("🌟 Some of your health metrics could use attention. Focus on a balanced diet, regular exercise, and adequate rest to improve your overall health.")

        # If everything is normal
        if not tips and not vital_issues and not lifestyle_issues and not test_issues:
            tips.append("🌟 Fantastic job maintaining your health! Keep up your great habits with regular exercise, balanced nutrition, and good sleep. You're doing wonderfully!")

        return tips


    def generate_report(self, health_data: dict) -> dict:
        """
        Generate a comprehensive health report using weighted scoring across multiple health categories.
        This is the main method that maintains backward compatibility with existing code.
        """
        try:
            # Extract the actual health data if it's in a nested format
            if isinstance(health_data, dict) and "data" in health_data:
                health_data = health_data["data"]

            # Calculate scores for each category
            vital_results = self.calculate_vital_score(health_data)
            lifestyle_results = self.calculate_lifestyle_score(health_data)
            test_results = self.calculate_test_score(health_data)

            # Calculate weighted total score
            categories_with_data = 0
            weighted_score = 0
            adjusted_weight_sum = 0

            # Check if we have any data in each category (not just score > 0)
            has_vital_data = any(vital in health_data and health_data[vital] not in [None, "", "null", "Unknown"]
                               for vital in self.vital_weights.keys())
            has_lifestyle_data = any(factor in health_data and health_data[factor] not in [None, "", "null", "Unknown"]
                                   for factor in self.lifestyle_weights.keys())
            has_test_data = any(test in health_data and health_data[test] not in [None, "", "null", "Unknown"]
                              for test in self.test_weights.keys())

            if has_vital_data:
                weighted_score += vital_results["score"] * self.category_weights["vitals"]
                adjusted_weight_sum += self.category_weights["vitals"]
                categories_with_data += 1

            if has_lifestyle_data:
                weighted_score += lifestyle_results["score"] * self.category_weights["lifestyle"]
                adjusted_weight_sum += self.category_weights["lifestyle"]
                categories_with_data += 1

            if has_test_data:
                weighted_score += test_results["score"] * self.category_weights["test_results"]
                adjusted_weight_sum += self.category_weights["test_results"]
                categories_with_data += 1

            # Calculate final score
            if adjusted_weight_sum > 0:
                final_score = round(weighted_score / adjusted_weight_sum)
            elif categories_with_data == 0:
                # Fall back to legacy scoring if no category data is available
                total_score = 0
                max_score = 0

                for key, value in health_data.items():
                    if value in [None, '', 'null']:
                        continue  # Skip missing or null fields

                    status = self.evaluate_health_metric(key, value)
                    if key in self.scoring_criteria:
                        max_score += 5
                        if status == "Normal" or status == "Negative":
                            total_score += 5

                final_score = round((total_score / max_score) * 100) if max_score > 0 else 0
            else:
                final_score = 0

            # Determine health status
            if final_score >= 85:
                status = "Excellent"
            elif final_score >= 70:
                status = "Good"
            elif final_score >= 50:
                status = "Fair"
            else:
                status = "Poor"

            # Generate improvement tips
            improvement_tips = self.generate_improvement_tips(
                vital_results["issues"],
                lifestyle_results["issues"],
                test_results["issues"]
            )

            # Add device recommendation prompt (following agent_app pattern)
            device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your health score results? (Yes/No)**"

            # Add values with units
            values_with_units = {}
            # Add scoring_criteria values
            for key, meta in self.scoring_criteria.items():
                if key in health_data and health_data[key] not in [None, '', 'null']:
                    val = health_data[key]
                    # Support dict input for value/unit, e.g., {"value": 70, "unit": "kg"}
                    if isinstance(val, dict) and "value" in val and "unit" in val:
                        values_with_units[key] = f"{val['value']} {val['unit']}"
                    elif isinstance(val, (int, float)):
                        # Use default unit from meta if not provided as dict
                        unit = meta.get("unit", "")
                        values_with_units[key] = f"{val} {unit}"
                    elif isinstance(val, str):
                        # Check if the string already contains units
                        if any(char.isalpha() or char in ['°', '/', '%'] for char in str(val)):
                            values_with_units[key] = str(val)  # Keep original string with units
                        else:
                            # Add default unit if it's just a number
                            unit = meta.get("unit", "")
                            values_with_units[key] = f"{val} {unit}"
                    elif isinstance(val, dict):
                        values_with_units[key] = val
            # Add lifestyle_ranges values
            if hasattr(self, "lifestyle_ranges"):
                for key, meta in self.lifestyle_ranges.items():
                    if key in health_data and health_data[key] not in [None, '', 'null']:
                        val = health_data[key]
                        unit = meta.get("unit", "")
                        if isinstance(val, (int, float)):
                            values_with_units[key] = f"{val} {unit}"
                        elif isinstance(val, str):
                            values_with_units[key] = f"{val}"
            # Add any other values not already included
            for key, val in health_data.items():
                if key not in values_with_units and val not in [None, '', 'null']:
                    values_with_units[key] = val

            # Create the final result - maintain backward compatibility
            result = {
                "Total Score": final_score,
                "Health Status": status,
                "Vitals Needing Improvement": ", ".join(vital_results["issues"]) if vital_results["issues"] else "None",
                "Improvement Tips": " ".join(improvement_tips) if improvement_tips else "Keep maintaining your health!",
                "Values With Units": values_with_units
            }

            # Add enhanced data for new clients that can use it
            result.update({
                "timestamp": datetime.now().isoformat(),
                "Category Scores": {
                    "Vitals": round(vital_results["score"]),
                    "Lifestyle": round(lifestyle_results["score"]),
                    "Test Results": round(test_results["score"])
                },
                "Detailed Issues": {
                    "Vitals": vital_results["issues"],
                    "Lifestyle": lifestyle_results["issues"],
                    "Test Results": test_results["issues"]
                },
                "Detailed Improvement Tips": improvement_tips,
                "device_recommendation_prompt": device_recommendation_prompt,
                "test_type": "realtime_health_score"
            })

            return result

        except Exception as e:
            logging.error(f"Error generating health report: {str(e)}")
            return {
                "Total Score": 0,
                "Health Status": "Unknown",
                "Vitals Needing Improvement": "Error in analysis",
                "Improvement Tips": "Please try again or consult a healthcare professional.",
                "error": f"Failed to analyze health data: {str(e)}"
            }

    def analyze_health_trends(self, current_data, historical_data):
        """
        Analyze trends in health data over time to identify improvements or deteriorations.

        Args:
            current_data: The most recent health data
            historical_data: List of previous health data points in chronological order

        Returns:
            Dictionary with trend analysis results
        """
        if not historical_data:
            return {"message": "No historical data available for trend analysis"}

        trends = {
            "improving": [],
            "deteriorating": [],
            "stable": [],
            "significant_changes": []
        }

        # Get the most recent historical data point for comparison
        previous_data = historical_data[-1]

        # Compare vital signs
        for vital in self.vital_weights.keys():
            if vital in current_data and vital in previous_data:
                current_value = current_data[vital]
                previous_value = previous_data[vital]

                if isinstance(current_value, (int, float)) and isinstance(previous_value, (int, float)):
                    percent_change = ((current_value - previous_value) / previous_value) * 100 if previous_value != 0 else 0

                    if abs(percent_change) >= self.trend_thresholds["vital_change"]:
                        trends["significant_changes"].append({
                            "parameter": vital,
                            "previous": previous_value,
                            "current": current_value,
                            "percent_change": round(percent_change, 1)
                        })

                    if self.evaluate_health_metric(vital, current_value) == "Normal" and self.evaluate_health_metric(vital, previous_value) != "Normal":
                        trends["improving"].append(vital)
                    elif self.evaluate_health_metric(vital, current_value) != "Normal" and self.evaluate_health_metric(vital, previous_value) == "Normal":
                        trends["deteriorating"].append(vital)
                    else:
                        trends["stable"].append(vital)

        # Compare overall scores if available
        if "Total Score" in current_data and "Total Score" in previous_data:
            current_score = current_data["Total Score"]
            previous_score = previous_data["Total Score"]

            score_change = current_score - previous_score

            if abs(score_change) >= self.trend_thresholds["score_change"]:
                trends["significant_changes"].append({
                    "parameter": "Total Health Score",
                    "previous": previous_score,
                    "current": current_score,
                    "change": score_change
                })

            if score_change > 0:
                trends["overall"] = "Improving"
            elif score_change < 0:
                trends["overall"] = "Deteriorating"
            else:
                trends["overall"] = "Stable"

        return trends


# Create a Tool instance for use with LangChain
health_score_analysis_tool = Tool(
    name="HealthScoreAnalysis",
    func=lambda x: json.dumps(HealthScoreAnalysisTool().generate_report(json.loads(x)), indent=2),
    description="Analyzes comprehensive health data including vitals, lifestyle factors, and test results to generate a holistic health score with personalized recommendations."
)
