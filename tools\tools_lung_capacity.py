import json
import logging
from langchain.tools import Tool
from typing import Dict, List, Any, Optional, Union

# Note: Height-based calculations removed - analysis now works with BMI only

def analyze_lung_capacity(data_json: str) -> str:
    """
    Analyzes spirometry data to assess lung capacity and respiratory health.
    Uses pattern recognition and rule-based triggers to identify potential respiratory issues.

    Args:
        data_json: JSON string containing spirometry data and patient information

    Returns:
        JSON string with analysis results, risk assessment, and recommendations
    """
    try:
        # Parse input data
        data = json.loads(data_json)

        # Extract spirometry parameters
        spirometry_data = data.get("data", {})

        # Initialize results
        analysis_results = []
        risk_factors = []
        recommendations = []
        respiratory_conditions = []

        # Normal ranges are built into the analysis logic below (FEV1, FVC ≥80%, FEV1/FVC ≥0.7, etc.)

        # Extract patient information - prioritizing the core parameters you specified
        age = spirometry_data.get("Age", spirometry_data.get("age", 0))
        sex = spirometry_data.get("Sex", spirometry_data.get("sex", ""))
        gender = "male" if sex.lower() == "male" else "female"  # Normalize for peak flow calculation
        bmi = spirometry_data.get("BMI", spirometry_data.get("bmi", None))  # BMI value
        ethnicity = spirometry_data.get("Ethnicity", spirometry_data.get("ethnicity", spirometry_data.get("Race", "caucasian")))
        smoker = spirometry_data.get("Smoker", spirometry_data.get("smoker", spirometry_data.get("Smoking_Status", "")))

        # Convert BMI to float if it's a string
        if bmi is not None:
            try:
                bmi = float(bmi)
            except (ValueError, TypeError):
                bmi = None

        # Normalize smoker status
        if isinstance(smoker, bool):
            smoking_status = "current smoker" if smoker else "non-smoker"
        elif isinstance(smoker, str):
            smoking_status = smoker.lower()
        else:
            smoking_status = "unknown"

        # Extract FEV1 - the key measurement you specified
        fev1 = spirometry_data.get("FEV1", spirometry_data.get("fev1", spirometry_data.get("Fev1", None)))
        fev1 = None if fev1 == "Unknown" or fev1 == "" else fev1

        # Convert FEV1 to float if it's a string
        if fev1 is not None:
            try:
                fev1 = float(fev1)
            except (ValueError, TypeError):
                fev1 = None

        # Extract PEF - required parameter
        pef = spirometry_data.get("PEF", None)    # As percentage of predicted - REQUIRED
        pef = None if pef == "Unknown" else pef

        # Analysis will be based on provided values without height-based expected value calculations

        # Validate core parameters (FEV1 and PEF are required for analysis)
        core_params_available = {
            "Age": age > 0,
            "Sex": sex != "",
            "BMI": bmi is not None and bmi > 0,
            "Ethnicity": ethnicity != "",
            "Smoker": smoking_status != "unknown",
            "FEV1": fev1 is not None,
            "PEF": pef is not None
        }

        fvc = spirometry_data.get("FVC", None)    # As percentage of predicted
        fvc = None if fvc == "Unknown" else fvc

        fev1_fvc_ratio = spirometry_data.get("FEV1_FVC_ratio", None)
        fev1_fvc_ratio = None if fev1_fvc_ratio == "Unknown" else fev1_fvc_ratio

        # Optional parameters - FEV2, FEV3
        fev2 = spirometry_data.get("FEV2", None)    # As percentage of predicted - OPTIONAL
        fev2 = None if fev2 == "Unknown" else fev2

        fev3 = spirometry_data.get("FEV3", None)    # As percentage of predicted - OPTIONAL
        fev3 = None if fev3 == "Unknown" else fev3

        fef25_75 = spirometry_data.get("FEF25_75", None)
        fef25_75 = None if fef25_75 == "Unknown" else fef25_75

        # Display user spirometry data first
        analysis_results.append("📋 **YOUR SPIROMETRY TEST RESULTS:**")
        analysis_results.append("=" * 50)

        # Display provided spirometry values
        if age > 0:
            analysis_results.append(f"👤 Patient: {age}-year-old {gender.lower()}")
        if bmi is not None and bmi > 0:
            bmi_category = "underweight" if bmi < 18.5 else "normal weight" if bmi < 25 else "overweight" if bmi < 30 else "obese"
            analysis_results.append(f"⚖️ BMI: {bmi:.1f} ({bmi_category})")
        if ethnicity:
            analysis_results.append(f"🧬 Ethnicity: {ethnicity}")
        if smoking_status != "unknown":
            smoking_emoji = "🚭" if "non" in smoking_status else "🚬"
            analysis_results.append(f"{smoking_emoji} Smoking status: {smoking_status}")

        analysis_results.append("")
        analysis_results.append("📊 **MEASURED VALUES:**")

        # Display all provided spirometry values
        if fev1 is not None:
            if fev1 <= 200:
                analysis_results.append(f"• FEV1 (Forced Expiratory Volume in 1 second): {fev1:.1f}% of predicted")
            else:
                analysis_results.append(f"• FEV1 (Forced Expiratory Volume in 1 second): {fev1:.2f}L")

        if pef is not None:
            if pef <= 200:
                analysis_results.append(f"• PEF (Peak Expiratory Flow): {pef:.0f}% of predicted")
            else:
                analysis_results.append(f"• PEF (Peak Expiratory Flow): {pef:.0f}L/min")

        if fvc is not None:
            if fvc <= 200:
                analysis_results.append(f"• FVC (Forced Vital Capacity): {fvc:.1f}% of predicted")
            else:
                analysis_results.append(f"• FVC (Forced Vital Capacity): {fvc:.2f}L")

        if fev1_fvc_ratio is not None:
            analysis_results.append(f"• FEV1/FVC Ratio: {fev1_fvc_ratio:.2f}")

        if fev2 is not None:
            if fev2 <= 200:
                analysis_results.append(f"• FEV2 (Forced Expiratory Volume in 2 seconds): {fev2:.1f}% of predicted")
            else:
                analysis_results.append(f"• FEV2 (Forced Expiratory Volume in 2 seconds): {fev2:.2f}L")

        if fev3 is not None:
            if fev3 <= 200:
                analysis_results.append(f"• FEV3 (Forced Expiratory Volume in 3 seconds): {fev3:.1f}% of predicted")
            else:
                analysis_results.append(f"• FEV3 (Forced Expiratory Volume in 3 seconds): {fev3:.2f}L")

        # Display additional parameters if available
        additional_params = []
        if spirometry_data.get("FEF25_75") is not None and spirometry_data.get("FEF25_75") != "Unknown":
            additional_params.append(f"FEF25-75: {spirometry_data.get('FEF25_75')}%")
        if spirometry_data.get("TLC") is not None and spirometry_data.get("TLC") != "Unknown":
            additional_params.append(f"TLC: {spirometry_data.get('TLC')}%")
        if spirometry_data.get("RV") is not None and spirometry_data.get("RV") != "Unknown":
            additional_params.append(f"RV: {spirometry_data.get('RV')}%")
        if spirometry_data.get("DLCO") is not None and spirometry_data.get("DLCO") != "Unknown":
            additional_params.append(f"DLCO: {spirometry_data.get('DLCO')}%")

        if additional_params:
            for param in additional_params:
                analysis_results.append(f"• {param}")

        analysis_results.append("")
        analysis_results.append("🔍 **DETAILED ANALYSIS:**")
        analysis_results.append("=" * 50)

        # Analysis based on provided values without height-based expected value calculations

        # Enhanced analysis with direct value assessment (assuming values are percentages of predicted)
        if fev1 is not None and fev1 > 0:
            # Treat values as percentages of predicted if ≤200, otherwise as absolute values
            if fev1 <= 200:  # Assume it's a percentage
                analysis_results.append(f"FEV1: {fev1}% of predicted")

                # Add severity assessment based on percentage
                if fev1 >= 80:
                    analysis_results.append("✅ FEV1 is within normal range")
                elif fev1 >= 70:
                    analysis_results.append("⚠️ FEV1 shows mild reduction")
                elif fev1 >= 60:
                    analysis_results.append("🟡 FEV1 shows moderate reduction")
                elif fev1 >= 50:
                    analysis_results.append("🟠 FEV1 shows moderately severe reduction")
                else:
                    analysis_results.append("🔴 FEV1 shows severe reduction")
            else:  # Assume it's an absolute value in liters
                analysis_results.append(f"FEV1: {fev1:.2f}L (absolute value)")

        if fvc is not None and fvc > 0:
            if fvc <= 200:  # Assume it's a percentage
                analysis_results.append(f"FVC: {fvc}% of predicted")

                # Add severity assessment
                if fvc >= 80:
                    analysis_results.append("✅ FVC is within normal range")
                elif fvc >= 70:
                    analysis_results.append("⚠️ FVC shows mild reduction")
                else:
                    analysis_results.append("🟡 FVC shows significant reduction")
            else:  # Assume it's an absolute value in liters
                analysis_results.append(f"FVC: {fvc:.2f}L (absolute value)")

        if pef is not None and pef > 0:
            if pef <= 200:  # Assume it's a percentage
                analysis_results.append(f"PEF: {pef}% of predicted")

                # Add severity assessment
                if pef >= 80:
                    analysis_results.append("✅ Peak flow is within normal range")
                else:
                    analysis_results.append("⚠️ Peak flow is reduced - may indicate airway obstruction")
            else:  # Assume it's an absolute value in L/min
                analysis_results.append(f"PEF: {pef:.0f}L/min (absolute value)")

        # Add comprehensive demographic insights without height
        demographic_info = f"📊 Analysis for {age}-year-old {gender.lower()}, {ethnicity} ethnicity"
        if bmi is not None and bmi > 0:
            bmi_category = "underweight" if bmi < 18.5 else "normal" if bmi < 25 else "overweight" if bmi < 30 else "obese"
            demographic_info += f", BMI: {bmi:.1f} ({bmi_category})"
        analysis_results.append(demographic_info)

        # Add smoking status insight
        if smoking_status != "unknown":
            smoking_emoji = "🚭" if "non" in smoking_status else "🚬"
            analysis_results.append(f"{smoking_emoji} Smoking status: {smoking_status}")

        # Add ethnicity-specific information (informational only, no calculations)
        ethnicity_factors = {
            "asian": 0.93, "latino": 0.93, "hispanic": 0.93,
            "african": 0.87, "african american": 0.87, "black": 0.87,
            "caucasian": 1.0, "white": 1.0, "middle eastern": 1.0
        }
        ethnicity_factor = ethnicity_factors.get(ethnicity.lower(), 1.0)
        if ethnicity_factor != 1.0:
            analysis_results.append(f"🧬 Note: {ethnicity} ethnicity typically has adjustment factor of {ethnicity_factor} in spirometry calculations")

        # Analyze additional lung capacity parameters if available
        tlc = spirometry_data.get("TLC", None)
        rv = spirometry_data.get("RV", None)
        dlco = spirometry_data.get("DLCO", None)

        # Add analysis for additional parameters
        if tlc is not None and tlc != "Unknown":
            if tlc < 80:
                analysis_results.append(f"🫁 TLC (Total Lung Capacity): {tlc}% - reduced, suggesting restrictive disease")
                risk_factors.append("Reduced TLC")
            else:
                analysis_results.append(f"✅ TLC (Total Lung Capacity): {tlc}% - within normal range")

        if rv is not None and rv != "Unknown":
            if rv > 120:
                analysis_results.append(f"🫁 RV (Residual Volume): {rv}% - elevated, suggesting air trapping")
                risk_factors.append("Elevated RV")
            elif rv < 75:
                analysis_results.append(f"🫁 RV (Residual Volume): {rv}% - reduced")
                risk_factors.append("Reduced RV")
            else:
                analysis_results.append(f"✅ RV (Residual Volume): {rv}% - within normal range")

        if dlco is not None and dlco != "Unknown":
            if dlco < 80:
                analysis_results.append(f"🫁 DLCO (Diffusion Capacity): {dlco}% - reduced, may indicate gas exchange impairment")
                risk_factors.append("Reduced DLCO")
            else:
                analysis_results.append(f"✅ DLCO (Diffusion Capacity): {dlco}% - within normal range")

        # Track missing critical parameters (FEV1 and PEF are required)
        missing_parameters = []
        critical_params = ["FEV1", "PEF"]  # Both FEV1 and PEF are required for basic analysis
        optional_params = ["FVC", "FEV1_FVC_ratio", "FEV2", "FEV3", "FEF25_75", "TLC", "RV", "DLCO"]  # All other parameters are optional
        for param in critical_params:
            if param not in spirometry_data or spirometry_data[param] is None or spirometry_data[param] == "Unknown":
                missing_parameters.append(param)

        # Enhanced FEV1 analysis - the core parameter (values assumed to be % of predicted if ≤200)
        if fev1 is not None:
            # Treat as percentage if ≤200, otherwise as absolute value
            fev1_percent = fev1 if fev1 <= 200 else fev1  # No conversion without expected values

            if fev1_percent < 80:
                severity = "mild" if fev1_percent >= 70 else "moderate" if fev1_percent >= 60 else "moderately severe" if fev1_percent >= 50 else "severe" if fev1_percent >= 35 else "very severe"
                if fev1 <= 200:
                    analysis_results.append(f"🚨 FEV1 indicates {severity} airflow limitation ({fev1_percent:.1f}% of predicted)")
                else:
                    analysis_results.append(f"🚨 FEV1: {fev1_percent:.2f}L - {severity} reduction")
                risk_factors.append("Reduced FEV1")

                # Add comprehensive age-specific insights
                if age and age < 40:
                    analysis_results.append("⚠️ Reduced FEV1 at young age suggests possible asthma or congenital condition")
                    if "current" in smoking_status:
                        analysis_results.append("🚬 Early smoking damage - high priority for cessation")
                elif age and age > 65:
                    analysis_results.append("📊 Age-related decline may contribute to reduced FEV1")
                    if fev1_percent < 60:
                        analysis_results.append("👴 Significant decline in older adult - consider COPD evaluation")

                # Add BMI-related insights for FEV1
                if bmi:
                    if bmi >= 30:
                        analysis_results.append("⚖️ Obesity (BMI ≥30) may contribute to reduced lung function")
                        recommendations.append("🏃 Weight reduction may improve respiratory function")
                    elif bmi < 18.5:
                        analysis_results.append("⚖️ Underweight status may indicate respiratory muscle weakness")

                # Add smoking-specific FEV1 insights
                if "current" in smoking_status:
                    analysis_results.append("🚬 Current smoking is likely contributing to FEV1 reduction")
                    if fev1_percent < 50:
                        analysis_results.append("🔴 URGENT: Severe FEV1 reduction in smoker - immediate cessation critical")
                elif "former" in smoking_status:
                    analysis_results.append("🚭 Previous smoking may have contributed to current FEV1 reduction")
            else:
                if fev1 <= 200:
                    analysis_results.append(f"✅ FEV1: {fev1_percent:.1f}% of predicted - within normal range")
                else:
                    analysis_results.append(f"✅ FEV1: {fev1_percent:.2f}L - within expected range")
                if "current" in smoking_status:
                    analysis_results.append("⚠️ Normal FEV1 despite smoking - cessation now prevents future decline")

        # Enhanced analysis when only FEV1 and PEF are available
        if fev1 is not None and pef is not None and fvc is None and fev1_fvc_ratio is None:
            analysis_results.append("📊 **ANALYSIS BASED ON FEV1 AND PEF PATTERNS:**")

            # Determine FEV1 and PEF values for analysis
            fev1_val = fev1 if fev1 <= 200 else fev1
            pef_val = pef if pef <= 200 else pef

            # Detailed pattern analysis
            if fev1_val < 80 and pef_val < 80:
                analysis_results.append("🚨 **OBSTRUCTIVE PATTERN DETECTED:**")
                analysis_results.append("   • Both FEV1 and PEF are reduced, strongly suggesting airway obstruction")
                analysis_results.append("   • This pattern is consistent with conditions that narrow the airways")

                # Add potential conditions based on FEV1 and PEF alone
                if smoking_status and ("current" in smoking_status.lower() or "former" in smoking_status.lower()):
                    respiratory_conditions.append("Possible COPD")
                    analysis_results.append("🚬 **COPD LIKELY:** Smoking history + reduced FEV1 and PEF strongly suggests COPD")
                    if age and age > 40:
                        analysis_results.append("   • Age >40 with smoking history makes COPD highly probable")
                    if fev1_val < 50:
                        analysis_results.append("   • Severe reduction suggests advanced COPD requiring immediate attention")
                else:
                    respiratory_conditions.append("Possible asthma")
                    analysis_results.append("🫁 **ASTHMA POSSIBLE:** Reduced FEV1 and PEF without smoking history suggests asthma")
                    if age and age < 40:
                        analysis_results.append("   • Young age makes asthma more likely than COPD")

                # Add severity assessment
                if fev1_val < 50 or pef_val < 50:
                    analysis_results.append("🔴 **SEVERE OBSTRUCTION:** Immediate medical evaluation needed")
                elif fev1_val < 60 or pef_val < 60:
                    analysis_results.append("🟠 **MODERATE-SEVERE OBSTRUCTION:** Prompt medical attention recommended")
                else:
                    analysis_results.append("🟡 **MILD-MODERATE OBSTRUCTION:** Medical evaluation advised")

            elif fev1_val < 80 and pef_val >= 80:
                analysis_results.append("📊 **UNUSUAL PATTERN:**")
                analysis_results.append("   • Reduced FEV1 with normal PEF is uncommon")
                analysis_results.append("   • May suggest early restrictive disease or measurement variability")
                analysis_results.append("   • Complete spirometry with FVC strongly recommended")

            elif fev1_val >= 80 and pef_val < 80:
                analysis_results.append("📊 **VARIABLE OBSTRUCTION PATTERN:**")
                analysis_results.append("   • Normal FEV1 with reduced PEF suggests variable airway obstruction")
                analysis_results.append("   • This pattern is often seen in:")
                analysis_results.append("     - Early or mild asthma")
                analysis_results.append("     - Exercise-induced bronchospasm")
                analysis_results.append("     - Vocal cord dysfunction")
                respiratory_conditions.append("Possible variable airway obstruction")

            else:
                analysis_results.append("✅ **NORMAL PATTERN:**")
                analysis_results.append("   • Both FEV1 and PEF are within normal ranges")
                analysis_results.append("   • No evidence of significant airway obstruction")
                if smoking_status and "current" in smoking_status.lower():
                    analysis_results.append("⚠️ **WARNING:** Normal values now, but continued smoking will cause decline")

            analysis_results.append("")
            analysis_results.append("📋 **RECOMMENDATION:** FVC and FEV1/FVC ratio would provide definitive diagnosis")

        if fvc is not None:
            # Treat as percentage if ≤200, otherwise as absolute value
            fvc_percent = fvc if fvc <= 200 else fvc  # No conversion without expected values

            if fvc_percent < 80:
                severity = "mild" if fvc_percent >= 70 else "moderate" if fvc_percent >= 60 else "severe" if fvc_percent < 60 else ""
                if fvc <= 200:
                    analysis_results.append(f"🚨 FVC indicates {severity} restrictive pattern ({fvc_percent:.1f}% of predicted)")
                else:
                    analysis_results.append(f"🚨 FVC: {fvc_percent:.2f}L - {severity} reduction")
                risk_factors.append("Reduced FVC")

                # Gender-specific insights
                if gender.lower() == "female" and fvc_percent < 70:
                    analysis_results.append("👩 Significantly reduced FVC in females may indicate respiratory muscle weakness")
                elif gender.lower() == "male" and fvc_percent < 70:
                    analysis_results.append("👨 Significantly reduced FVC in males may indicate chest wall restriction")

        # Optional FEV2 analysis
        if fev2 is not None:
            # Treat as percentage if ≤200, otherwise as absolute value
            fev2_percent = fev2 if fev2 <= 200 else fev2

            if fev2_percent < 80:
                if fev2 <= 200:
                    analysis_results.append(f"📊 FEV2: {fev2_percent:.1f}% of predicted - reduced")
                else:
                    analysis_results.append(f"📊 FEV2: {fev2_percent:.2f}L - reduced")
                risk_factors.append("Reduced FEV2")

                # FEV2 provides additional insight into airway obstruction progression
                if fev1 is not None:
                    fev1_percent = fev1 if fev1 <= 200 else fev1
                    if fev2_percent > fev1_percent:
                        analysis_results.append("📈 FEV2 > FEV1 suggests some airway recovery after initial second")
                    else:
                        analysis_results.append("📉 FEV2 ≤ FEV1 indicates persistent airway limitation")
            else:
                if fev2 <= 200:
                    analysis_results.append(f"✅ FEV2: {fev2_percent:.1f}% of predicted - within normal range")
                else:
                    analysis_results.append(f"✅ FEV2: {fev2_percent:.2f}L - within expected range")

        # Optional FEV3 analysis
        if fev3 is not None:
            # Treat as percentage if ≤200, otherwise as absolute value
            fev3_percent = fev3 if fev3 <= 200 else fev3

            if fev3_percent < 80:
                if fev3 <= 200:
                    analysis_results.append(f"📊 FEV3: {fev3_percent:.1f}% of predicted - reduced")
                else:
                    analysis_results.append(f"📊 FEV3: {fev3_percent:.2f}L - reduced")
                risk_factors.append("Reduced FEV3")

                # FEV3 analysis in context of FEV1 and FEV2
                if fev1 is not None and fev2 is not None:
                    fev1_percent = fev1 if fev1 <= 200 else fev1
                    fev2_percent = fev2 if fev2 <= 200 else fev2

                    if fev3_percent > fev2_percent > fev1_percent:
                        analysis_results.append("📈 Progressive improvement FEV1→FEV2→FEV3 suggests reversible obstruction")
                    elif fev3_percent < fev1_percent:
                        analysis_results.append("📉 FEV3 < FEV1 indicates severe persistent obstruction")
                elif fev1 is not None:
                    fev1_percent = fev1 if fev1 <= 200 else fev1
                    if fev3_percent > fev1_percent:
                        analysis_results.append("📈 FEV3 > FEV1 suggests some late airway recovery")
            else:
                if fev3 <= 200:
                    analysis_results.append(f"✅ FEV3: {fev3_percent:.1f}% of predicted - within normal range")
                else:
                    analysis_results.append(f"✅ FEV3: {fev3_percent:.2f}L - within expected range")

        if fev1_fvc_ratio is not None:
            if fev1_fvc_ratio < 0.7:
                analysis_results.append(f"🚨 FEV1/FVC ratio: {fev1_fvc_ratio:.2f} - indicates obstructive lung disease")
                risk_factors.append("Low FEV1/FVC ratio")
                respiratory_conditions.append("Obstructive lung disease")

                # Enhanced COPD vs Asthma differentiation using peak flow insights
                if smoking_status and ("current" in smoking_status.lower() or "former" in smoking_status.lower()):
                    respiratory_conditions.append("Possible COPD")
                    if age and age > 40:
                        analysis_results.append("🚬 Pattern consistent with COPD: age >40 + smoking history + obstruction")
                        if fev1 and fev1 < 50:
                            analysis_results.append("🔴 Severe COPD pattern - immediate pulmonary consultation recommended")
                    else:
                        analysis_results.append("⚠️ Early-onset COPD possible - consider alpha-1 antitrypsin deficiency")
                else:
                    respiratory_conditions.append("Possible asthma")
                    analysis_results.append("🫁 Pattern may indicate asthma - consider bronchodilator response testing")
                    if age and age < 30:
                        analysis_results.append("👶 Young age supports asthma diagnosis over COPD")

                # Add severity grading based on FEV1/FVC ratio
                if fev1_fvc_ratio < 0.5:
                    analysis_results.append("🔴 Severe obstruction (FEV1/FVC < 0.5)")
                elif fev1_fvc_ratio < 0.6:
                    analysis_results.append("🟠 Moderate-severe obstruction")
                else:
                    analysis_results.append("🟡 Mild-moderate obstruction")

            elif fev1_fvc_ratio > 0.9 and fvc and fvc < 80:
                analysis_results.append(f"📊 FEV1/FVC ratio: {fev1_fvc_ratio:.2f} with reduced FVC - suggests restrictive disease")
                respiratory_conditions.append("Possible restrictive lung disease")

                # Add restrictive disease insights
                if fvc and fvc < 60:
                    analysis_results.append("🔴 Severe restriction - consider interstitial lung disease")
                elif tlc and tlc < 70:
                    analysis_results.append("🟠 Confirmed restrictive pattern with reduced TLC")

            else:
                analysis_results.append(f"✅ FEV1/FVC ratio: {fev1_fvc_ratio:.2f} - within normal range (≥0.70)")

        # Enhanced PEF analysis (required parameter)
        if pef is not None:
            # Treat as percentage if ≤200, otherwise as absolute value
            pef_percent = pef if pef <= 200 else pef

            if pef_percent < 80:
                if pef <= 200:
                    analysis_results.append(f"🚨 Peak Flow: {pef_percent:.0f}% of predicted - indicates airway obstruction")
                else:
                    analysis_results.append(f"🚨 Peak Flow: {pef_percent:.0f}L/min - reduced")
                risk_factors.append("Reduced PEF")

                # Add PEF-specific insights
                if pef_percent < 50:
                    analysis_results.append("🔴 Severely reduced peak flow - may indicate acute exacerbation")
                elif pef_percent < 60:
                    analysis_results.append("🟠 Significantly reduced peak flow - poor asthma control if asthmatic")

                # Gender and age-specific PEF insights
                if gender.lower() == "male" and age and age > 50:
                    analysis_results.append("👨 Reduced PEF in older males often indicates COPD progression")
                elif gender.lower() == "female" and age and age < 40:
                    analysis_results.append("👩 Reduced PEF in young females often suggests asthma")
            else:
                if pef <= 200:
                    analysis_results.append(f"✅ Peak Flow: {pef_percent:.0f}% of predicted - within normal range")
                else:
                    analysis_results.append(f"✅ Peak Flow: {pef_percent:.0f}L/min - within expected range")

        if fef25_75 is not None:
            if fef25_75 < 60:
                analysis_results.append(f"🚨 FEF25-75: {fef25_75}% - indicates small airway dysfunction")
                risk_factors.append("Reduced FEF25-75")

                if fev1_fvc_ratio and fev1_fvc_ratio >= 0.7:
                    analysis_results.append("⚠️ Normal FEV1/FVC with reduced FEF25-75 suggests early small airway disease")
                    if smoking_status and "current" in smoking_status.lower():
                        analysis_results.append("🚬 Early smoking-related small airway damage detected")
            else:
                analysis_results.append(f"✅ FEF25-75: {fef25_75}% - small airways functioning normally")

        # Determine overall respiratory risk level
        risk_level = "Low"
        if len(risk_factors) >= 3 or "Low FEV1/FVC ratio" in risk_factors:
            risk_level = "High"
        elif len(risk_factors) >= 1:
            risk_level = "Moderate"

        # Generate detailed recommendations based on specific findings and user's results
        recommendations.append("🎯 **PERSONALIZED RECOMMENDATIONS BASED ON YOUR RESULTS:**")
        recommendations.append("")

        if "Obstructive lung disease" in respiratory_conditions:
            if "Possible COPD" in respiratory_conditions:
                recommendations.append("🫁 **COPD Management Plan:**")
                fev1_val = f"{fev1:.1f}%" if fev1 <= 200 else f"{fev1:.2f}L"
                recommendations.append(f"   • Your FEV1 of {fev1_val} indicates we need to focus on preventing further decline")
                recommendations.append("   • Bronchodilator response testing recommended to confirm COPD and assess how much your airways can improve with medication")

                if smoking_status and "current" in smoking_status.lower():
                    recommendations.append("🚭 **URGENT - Smoking Cessation:**")
                    recommendations.append("   • This is your #1 priority - smoking cessation can reduce COPD progression by 50%")
                    recommendations.append("   • Consider nicotine replacement therapy, prescription medications, or counseling programs")
                    recommendations.append("   • Your current lung function will continue to decline rapidly if smoking continues")
                elif smoking_status and "former" in smoking_status.lower():
                    recommendations.append("🚭 **Smoking History Impact:**")
                    recommendations.append("   • Great job quitting smoking! This has already helped slow disease progression")
                    recommendations.append("   • Continue to avoid secondhand smoke and respiratory irritants")

                recommendations.append("💊 **Medication Considerations:**")
                recommendations.append("   • Short-acting bronchodilators (SABA) for immediate symptom relief")
                recommendations.append("   • Long-acting bronchodilators (LABA/LAMA) for daily maintenance therapy")

                # COPD-specific recommendations based on actual FEV1 values
                if fev1 and fev1 < 50:
                    recommendations.append("🔴 **Severe COPD Management:**")
                    recommendations.append(f"   • Your FEV1 of {fev1_val} indicates severe COPD requiring comprehensive care")
                    recommendations.append("   • Pulmonary rehabilitation program strongly recommended")
                    recommendations.append("   • Annual pneumonia vaccination and yearly flu shots are essential")
                    recommendations.append("   • Consider oxygen therapy evaluation")
                    recommendations.append("   • Emergency action plan needed for exacerbations")
                elif fev1 and fev1 < 80:
                    recommendations.append("🟡 **Moderate COPD Management:**")
                    recommendations.append(f"   • Your FEV1 of {fev1_val} shows moderate impairment - early intervention is key")
                    recommendations.append("   • Regular exercise within your tolerance limits (start with 10-15 minutes daily)")
                    recommendations.append("   • Breathing exercises and techniques to improve efficiency")
                    recommendations.append("   • Annual flu vaccination recommended")

                recommendations.append("👨‍⚕️ **Specialist Care:**")
                recommendations.append("   • Pulmonologist consultation for COPD staging and comprehensive treatment plan")
                recommendations.append("   • Regular follow-up spirometry every 6-12 months to monitor progression")
                recommendations.append("")

            elif "Possible asthma" in respiratory_conditions:
                recommendations.append("🫁 **Asthma Management Plan:**")
                if pef is not None:
                    pef_val = f"{pef:.0f}%" if pef <= 200 else f"{pef:.0f}L/min"
                    recommendations.append(f"   • Your peak flow of {pef_val} suggests variable airway obstruction typical of asthma")
                if fev1_fvc_ratio is not None and fev1_fvc_ratio < 0.7:
                    recommendations.append(f"   • Your FEV1/FVC ratio of {fev1_fvc_ratio:.2f} confirms obstructive pattern")

                recommendations.append("📱 **Home Monitoring:**")
                recommendations.append("   • Daily peak flow monitoring to track asthma control and detect early changes")
                recommendations.append("   • Keep a symptom diary to identify patterns and triggers")
                recommendations.append("   • Learn to recognize early warning signs of worsening asthma")

                recommendations.append("🧪 **Trigger Identification:**")
                recommendations.append("   • Comprehensive allergy testing to identify specific triggers")
                recommendations.append("   • Environmental assessment (dust mites, pet dander, mold, pollen)")
                recommendations.append("   • Consider occupational or exercise-induced triggers")

                recommendations.append("💊 **Medication Strategy:**")
                if fev1 and fev1 < 80:
                    recommendations.append("   • Controller medication (inhaled corticosteroids) likely needed for daily use")
                    recommendations.append("   • Quick-relief inhaler (albuterol) for acute symptoms")
                else:
                    recommendations.append("   • Quick-relief inhaler (albuterol) for symptoms as needed")
                    recommendations.append("   • Controller medication may be needed if symptoms occur frequently")

                recommendations.append("👨‍⚕️ **Professional Care:**")
                recommendations.append("   • Asthma action plan development with your healthcare provider")
                recommendations.append("   • Regular follow-up to adjust medications based on control level")

                # Age-specific asthma recommendations with detailed explanations
                if age and age < 18:
                    recommendations.append("👶 **Pediatric Considerations:**")
                    recommendations.append("   • Growth monitoring important with inhaled steroids")
                    recommendations.append("   • School asthma management plan needed")
                    recommendations.append("   • Age-appropriate inhaler technique training")
                elif age and age > 65:
                    recommendations.append("👴 **Adult-Onset Asthma:**")
                    recommendations.append("   • Rule out COPD overlap syndrome (ACOS)")
                    recommendations.append("   • Consider cardiac causes of breathing difficulties")
                    recommendations.append("   • Medication interactions review important")
                recommendations.append("")

        if "Possible restrictive lung disease" in respiratory_conditions:
            recommendations.append("🫁 Complete pulmonary function testing with lung volumes (TLC, RV)")
            recommendations.append("🩻 High-resolution chest CT to evaluate for interstitial changes")
            recommendations.append("� Consider autoimmune markers if connective tissue disease suspected")
            recommendations.append("👨‍⚕️ Pulmonologist referral for restrictive disease workup")

            # Restrictive-specific recommendations
            if dlco and dlco < 60:
                recommendations.append("🔴 Severely reduced DLCO - urgent evaluation needed")

        # Peak flow-specific recommendations
        if pef and pef < 60:
            recommendations.append("📱 Daily peak flow monitoring recommended")
            recommendations.append("� Develop action plan for peak flow <50% of personal best")

        # Small airway disease recommendations
        if fef25_75 and fef25_75 < 60:
            recommendations.append("🫁 Small airway disease detected - avoid respiratory irritants")
            if smoking_status and "current" in smoking_status.lower():
                recommendations.append("🚭 URGENT: Smoking cessation to prevent small airway damage progression")

        # Optional parameter recommendations
        optional_available = []
        optional_missing = []
        for param in optional_params:
            if param in spirometry_data and spirometry_data[param] is not None and spirometry_data[param] != "Unknown":
                optional_available.append(param)
            else:
                optional_missing.append(param)

        # Prioritize most important missing parameters
        if "FVC" in optional_missing and "FEV1_FVC_ratio" in optional_missing:
            recommendations.append("📋 FVC and FEV1/FVC ratio measurements recommended for complete respiratory assessment")
        elif "FVC" in optional_missing:
            recommendations.append("🫁 FVC (Forced Vital Capacity) measurement would help assess for restrictive lung disease")
        elif "FEV1_FVC_ratio" in optional_missing:
            recommendations.append("📊 FEV1/FVC ratio calculation would help differentiate obstructive vs restrictive patterns")

        if optional_missing and len(optional_missing) >= 3:
            recommendations.append("📋 Consider complete spirometry with FVC, FEV2, and FEV3 for comprehensive assessment")
        elif "FEV2" in optional_missing or "FEV3" in optional_missing:
            recommendations.append("⏱️ Extended spirometry (FEV2, FEV3) may provide additional insights into airway recovery")

        # Add detailed recommendations based on specific conditions found
        if not respiratory_conditions or respiratory_conditions == ["No specific respiratory conditions identified"]:
            if risk_level == "Low":
                recommendations.append("✅ **EXCELLENT LUNG FUNCTION - MAINTENANCE PLAN:**")
                if fev1 is not None:
                    fev1_display = f"{fev1:.1f}%" if fev1 <= 200 else f"{fev1:.2f}L"
                    recommendations.append(f"   • Your FEV1 of {fev1_display} is excellent - maintain this level")
                if pef is not None:
                    pef_display = f"{pef:.0f}%" if pef <= 200 else f"{pef:.0f}L/min"
                    recommendations.append(f"   • Your peak flow of {pef_display} shows good airway function")

                recommendations.append("🏃 **EXERCISE RECOMMENDATIONS:**")
                recommendations.append("   • Continue regular aerobic exercise (150 minutes/week moderate intensity)")
                recommendations.append("   • Include activities like brisk walking, swimming, cycling")
                recommendations.append("   • Strength training 2-3 times per week to maintain respiratory muscles")

                recommendations.append("🫁 **LUNG HEALTH MAINTENANCE:**")
                recommendations.append("   • Annual spirometry to monitor for any changes")
                recommendations.append("   • Avoid respiratory irritants (pollution, strong chemicals)")
                recommendations.append("   • Stay up-to-date with vaccinations (flu, pneumonia)")

                if smoking_status and "current" in smoking_status.lower():
                    recommendations.append("🚭 **CRITICAL - SMOKING CESSATION:**")
                    recommendations.append("   • Even with normal lung function, smoking will cause rapid decline")
                    recommendations.append("   • Quit now to prevent future COPD, lung cancer, and heart disease")
                    recommendations.append("   • Consider nicotine replacement, medications, or counseling")
                elif smoking_status and "former" in smoking_status.lower():
                    recommendations.append("🚭 **FORMER SMOKER - CONTINUED VIGILANCE:**")
                    recommendations.append("   • Great job quitting! Your lungs are benefiting")
                    recommendations.append("   • Continue avoiding secondhand smoke")
                    recommendations.append("   • Monitor for any breathing changes")

                if bmi and bmi >= 30:
                    recommendations.append("⚖️ **WEIGHT MANAGEMENT:**")
                    recommendations.append("   • Weight loss can improve breathing efficiency")
                    recommendations.append("   • Even 5-10% weight loss can make a difference")
                    recommendations.append("   • Combine diet and exercise for best results")

                recommendations.append("")
            else:
                recommendations.append("📊 **MONITORING RECOMMENDED:**")
                recommendations.append("   • Some parameters show mild reduction")
                recommendations.append("   • Complete spirometry with FVC recommended for full assessment")
                recommendations.append("   • Follow up in 6-12 months to monitor trends")
                recommendations.append("")

        # Enhanced general recommendations for moderate to high risk
        if risk_level in ["Moderate", "High"]:
            recommendations.append("💨 Diaphragmatic breathing exercises 10 minutes daily")
            recommendations.append("🏃 Pulmonary rehabilitation if available in your area")
            recommendations.append("😷 N95 masks in polluted environments")
            recommendations.append("🌡️ Annual flu vaccination to prevent respiratory infections")

            # Age-specific recommendations
            if age and age > 65:
                recommendations.append("� Regular pneumonia vaccination for older adults")

            # Gender-specific recommendations
            if gender.lower() == "female" and age and 20 <= age <= 40:
                recommendations.append("👩 Monitor lung function during pregnancy if planning family")

        # Determine confidence level based on core parameters availability
        core_params_score = sum(1 for available in core_params_available.values() if available)
        if core_params_score >= 6:  # Most core parameters available
            confidence_level = "High"
        elif core_params_score >= 4:  # Some core parameters available
            confidence_level = "Moderate"
        else:  # Few core parameters available
            confidence_level = "Low"

        # Create a doctor-like summary using actual spirometry results
        doctor_summary = ""

        # Add a greeting with specific results
        doctor_summary += f"Hi there! I've carefully reviewed your spirometry test results. "

        # Mention key measurements
        key_results = []
        if fev1 is not None:
            fev1_val = f"{fev1:.1f}%" if fev1 <= 200 else f"{fev1:.2f}L"
            key_results.append(f"FEV1 of {fev1_val}")
        if pef is not None:
            pef_val = f"{pef:.0f}%" if pef <= 200 else f"{pef:.0f}L/min"
            key_results.append(f"peak flow of {pef_val}")
        if fvc is not None:
            fvc_val = f"{fvc:.1f}%" if fvc <= 200 else f"{fvc:.2f}L"
            key_results.append(f"FVC of {fvc_val}")

        if key_results:
            doctor_summary += f"Your main measurements show {', '.join(key_results)}. "

        # Add specific analysis based on actual values
        if risk_level == "Low":
            doctor_summary += "These values indicate your lung function is performing well. "
            if fev1 is not None and fev1 >= 80:
                if fev1 <= 200:
                    doctor_summary += f"Your FEV1 of {fev1:.1f}% is in the normal range, which is excellent. "
                else:
                    doctor_summary += f"Your FEV1 of {fev1:.2f}L is in the normal range, which is excellent. "
            if pef is not None and pef >= 80:
                if pef <= 200:
                    doctor_summary += f"Your peak flow of {pef:.0f}% also shows good airway function. "
                else:
                    doctor_summary += f"Your peak flow of {pef:.0f}L/min also shows good airway function. "
        elif risk_level == "Moderate":
            doctor_summary += "I'm noticing some areas that need attention. "
            if fev1 is not None and fev1 < 80:
                severity = "mildly" if fev1 >= 70 else "moderately" if fev1 >= 60 else "significantly"
                if fev1 <= 200:
                    doctor_summary += f"Your FEV1 of {fev1:.1f}% is {severity} reduced. "
                else:
                    doctor_summary += f"Your FEV1 of {fev1:.2f}L is {severity} reduced. "
            if pef is not None and pef < 80:
                if pef <= 200:
                    doctor_summary += f"Your peak flow of {pef:.0f}% also shows some limitation. "
                else:
                    doctor_summary += f"Your peak flow of {pef:.0f}L/min also shows some limitation. "
        elif risk_level == "High":
            doctor_summary += "I'm seeing some significant concerns that we need to address promptly. "
            if fev1 is not None and fev1 < 60:
                if fev1 <= 200:
                    doctor_summary += f"Your FEV1 of {fev1:.1f}% shows substantial reduction. "
                else:
                    doctor_summary += f"Your FEV1 of {fev1:.2f}L shows substantial reduction. "
            if pef is not None and pef < 60:
                if pef <= 200:
                    doctor_summary += f"Your peak flow of {pef:.0f}% is also significantly reduced. "
                else:
                    doctor_summary += f"Your peak flow of {pef:.0f}L/min is also significantly reduced. "

        # Add FEV1/FVC ratio interpretation if available
        if fev1_fvc_ratio is not None:
            if fev1_fvc_ratio < 0.7:
                doctor_summary += f"Your FEV1/FVC ratio of {fev1_fvc_ratio:.2f} suggests an obstructive pattern. "
            else:
                doctor_summary += f"Your FEV1/FVC ratio of {fev1_fvc_ratio:.2f} is within normal limits. "

        # Add information about potential conditions with specific context
        if respiratory_conditions and respiratory_conditions != ["No specific respiratory conditions identified"]:
            doctor_summary += f"Based on your specific test pattern, these findings may be consistent with {', '.join(respiratory_conditions).lower()}. "

            # Add condition-specific context
            if "COPD" in str(respiratory_conditions):
                if smoking_status and "current" in smoking_status.lower():
                    doctor_summary += "Given your current smoking status, this pattern is particularly concerning. "
                elif smoking_status and "former" in smoking_status.lower():
                    doctor_summary += "Your smoking history likely contributes to these findings. "

            if "asthma" in str(respiratory_conditions).lower():
                doctor_summary += "This pattern often responds well to appropriate bronchodilator therapy. "

            doctor_summary += "These are preliminary findings based on your spirometry results - a complete evaluation by a pulmonologist would provide a definitive diagnosis. "

        # Add confidence note
        if confidence_level == "High":
            doctor_summary += "I have high confidence in this assessment given the comprehensive data available."
        elif confidence_level == "Moderate":
            doctor_summary += "This assessment is based on the key measurements available."
        elif confidence_level == "Low":
            doctor_summary += f"This is a preliminary assessment as we're missing some important measurements ({', '.join(missing_parameters)})."

        # Add device recommendation prompt (following agent_app pattern)
        device_recommendation_prompt = "\n\n🛒 **Would you like to see health monitoring devices from [TurboMedics](https://www.turbomedics.com/products) based on your lung capacity results? (Yes/No)**"

        # Create final result
        result = {
            "analysis": analysis_results,
            "respiratory_risk_level": risk_level,
            "potential_conditions": respiratory_conditions if respiratory_conditions else ["No specific respiratory conditions identified"],
            "recommendations": recommendations,
            "confidence_level": confidence_level,
            # "missing_parameters": missing_parameters,
            "doctor_summary": doctor_summary,
            "device_recommendation_prompt": device_recommendation_prompt,
            "test_type": "lung_capacity"
        }

        return json.dumps(result, indent=4)

    except Exception as e:
        logging.error(f"Error analyzing lung capacity: {str(e)}")
        return json.dumps({
            "error": f"Failed to analyze lung capacity: {str(e)}",
            "recommendations": ["⚠️ Unable to process spirometry data. Please consult a healthcare professional."]
        }, indent=4)

# Create the tool
lung_capacity_analyzer_tool = Tool(
    name="LungCapacityAnalyzer",
    func=analyze_lung_capacity,
    description="Analyzes spirometry data to assess lung capacity, identify respiratory risks like COPD/asthma, and provide respiratory health recommendations."
)
